#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无字幕版本的字幕补充下载器
"""

from subtitle_补充下载器 import parse_old_format_history, check_subtitle_exists

def test_parse_history():
    """测试历史记录解析"""
    print("测试历史记录解析...")
    
    videos_info = parse_old_format_history()
    
    if videos_info:
        print(f"✅ 成功解析 {len(videos_info)} 个视频记录")
        
        # 显示前5个记录
        print("\n前5个视频记录:")
        for i, (video_id, chinese_title, clean_title, channel_name) in enumerate(videos_info[:5], 1):
            print(f"{i}. ID: {video_id}")
            print(f"   频道: [{channel_name}]")
            print(f"   中文标题: {chinese_title}")
            print(f"   清理后标题: {clean_title}")
            
            # 检查字幕状态
            has_subtitle = check_subtitle_exists(clean_title)
            print(f"   字幕状态: {'✅已有' if has_subtitle else '❌缺失'}")
            print()
    else:
        print("❌ 没有找到视频记录")

def test_subtitle_statistics():
    """统计字幕情况"""
    print("统计字幕情况...")
    
    videos_info = parse_old_format_history()
    
    if not videos_info:
        print("❌ 没有视频记录可统计")
        return
    
    total = len(videos_info)
    has_subtitle = 0
    no_subtitle = 0
    
    for video_id, chinese_title, clean_title, channel_name in videos_info:
        if check_subtitle_exists(clean_title):
            has_subtitle += 1
        else:
            no_subtitle += 1
    
    print(f"📊 字幕统计:")
    print(f"   总视频数: {total}")
    print(f"   已有字幕: {has_subtitle} ({has_subtitle/total*100:.1f}%)")
    print(f"   缺失字幕: {no_subtitle} ({no_subtitle/total*100:.1f}%)")
    print(f"   需要下载: {no_subtitle} 个字幕")

def test_channel_statistics():
    """按频道统计"""
    print("按频道统计...")
    
    videos_info = parse_old_format_history()
    
    if not videos_info:
        print("❌ 没有视频记录可统计")
        return
    
    channel_stats = {}
    
    for video_id, chinese_title, clean_title, channel_name in videos_info:
        if channel_name not in channel_stats:
            channel_stats[channel_name] = {'total': 0, 'has_subtitle': 0, 'no_subtitle': 0}
        
        channel_stats[channel_name]['total'] += 1
        
        if check_subtitle_exists(clean_title):
            channel_stats[channel_name]['has_subtitle'] += 1
        else:
            channel_stats[channel_name]['no_subtitle'] += 1
    
    print(f"📊 按频道统计 (前10个频道):")
    print("-" * 80)
    
    # 按总数排序
    sorted_channels = sorted(channel_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    
    for i, (channel_name, stats) in enumerate(sorted_channels[:10], 1):
        total = stats['total']
        has = stats['has_subtitle']
        no = stats['no_subtitle']
        
        print(f"{i:2d}. [{channel_name}]")
        print(f"     总数: {total} | 有字幕: {has} | 缺失: {no} | 缺失率: {no/total*100:.1f}%")

if __name__ == "__main__":
    print("=" * 60)
    print("无字幕版本 - 字幕补充下载器测试")
    print("=" * 60)
    
    test_parse_history()
    print("-" * 60)
    test_subtitle_statistics()
    print("-" * 60)
    test_channel_statistics()
