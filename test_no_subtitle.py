#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无字幕版本测试脚本
用于验证无字幕版本的各个模块是否正常工作
"""

import sys
import os

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    try:
        import config
        print("✅ config 模块导入成功")
        
        import utils
        print("✅ utils 模块导入成功")
        
        import video_info
        print("✅ video_info 模块导入成功")
        
        import translation
        print("✅ translation 模块导入成功")
        
        import thumbnail_downloader
        print("✅ thumbnail_downloader 模块导入成功")
        
        import downloader
        print("✅ downloader 模块导入成功")
        
        import monitor
        print("✅ monitor 模块导入成功")
        
        import main
        print("✅ main 模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    try:
        from config import DOWNLOAD_PATHS, CHECK_INTERVAL, logger
        print(f"✅ 下载路径配置: {DOWNLOAD_PATHS}")
        print(f"✅ 检查间隔: {CHECK_INTERVAL} 秒")
        print("✅ 日志系统正常")
        
        # 检查是否移除了字幕相关配置
        try:
            from config import VTT_HAN_FOLDER, SRT_HAN_FOLDER
            print("⚠️  警告: 仍然包含字幕文件夹配置")
        except ImportError:
            print("✅ 已正确移除字幕文件夹配置")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_files():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    files_to_check = [
        'channels_videos_test.txt',
        'keywords.txt',
        'downloaded_videos.txt'
    ]
    
    all_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            all_exist = False
    
    return all_exist

def test_translation():
    """测试翻译功能"""
    print("\n=== 测试翻译功能 ===")
    try:
        from translation import translate_title_with_retry
        
        # 测试简单翻译
        test_title = "Hello World"
        result = translate_title_with_retry(test_title)
        print(f"✅ 翻译测试: '{test_title}' -> '{result}'")
        return True
    except Exception as e:
        print(f"❌ 翻译测试失败: {e}")
        return False

def test_utils():
    """测试工具函数"""
    print("\n=== 测试工具函数 ===")
    try:
        from utils import sanitize_filename, load_keywords, load_downloaded_history
        
        # 测试文件名清理
        test_filename = 'Test/File:Name*With?Illegal<Characters>'
        clean_filename = sanitize_filename(test_filename)
        print(f"✅ 文件名清理: '{test_filename}' -> '{clean_filename}'")
        
        # 测试加载关键词
        keywords = load_keywords()
        print(f"✅ 关键词加载: 共 {len(keywords)} 个关键词")
        
        # 测试加载历史记录
        load_downloaded_history()
        print("✅ 历史记录加载成功")
        
        return True
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YouTube下载系统 - 无字幕版本测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置测试", test_config),
        ("配置文件", test_files),
        ("翻译功能", test_translation),
        ("工具函数", test_utils)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！无字幕版本可以正常使用。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
