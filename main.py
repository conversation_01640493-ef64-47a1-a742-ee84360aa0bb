
from config import logger, DOWNLOAD_PATHS
from monitor import start_video_monitoring
import os
from fuzzywuzzy import fuzz

# 定义视频文件夹路径
video_folder = DOWNLOAD_PATHS["required"]

def get_file_size(file_path):
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except Exception as e:
        print(f"获取文件大小出错: {e}")
        return 0

def is_size_similar(size1, size2, tolerance=0.05):
    """
    判断两个文件大小是否相似
    tolerance: 允许的差异百分比（默认5%）
    """
    if size1 == 0 or size2 == 0:  # 避免除以零错误
        return False
    
    # 计算比例
    ratio = size1 / size2 if size1 <= size2 else size2 / size1
    
    # 如果比例大于(1-tolerance)，则认为大小相似
    return ratio > (1 - tolerance)

def is_duration_similar(duration1, duration2, tolerance=0.05):
    """
    判断两个视频时长是否相似
    tolerance: 允许的差异百分比（默认5%）
    """
    if duration1 is None or duration2 is None:
        return False
    
    ratio = duration1 / duration2 if duration1 <= duration2 else duration2 / duration1
    return ratio > (1 - tolerance)

def get_video_duration(file_path):
    """获取视频时长（秒）"""
    try:
        # 使用ffprobe获取视频时长
        import subprocess
        cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{file_path}"'
        output = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
        return float(output) if output else None
    except Exception as e:
        print(f"获取视频时长出错: {e}")
        return None

def filter_similar_videos(video_files, processed_videos, threshold=78, size_tolerance=0.05, duration_tolerance=0.05):
    """过滤掉与已处理文件名相似且大小相近的视频"""
    filtered_videos = []
    print(f"开始过滤视频。待处理视频：{video_files}，已处理视频：{processed_videos}")

    for video in video_files:
        is_duplicate = False
        video_path = os.path.join(video_folder, f"{video}.mp4")
        video_size = get_file_size(video_path)
        video_duration = get_video_duration(video_path)
        
        # 检查当前视频是否为短视频
        is_current_short = "_short" in video
        
        print(f"当前检查视频：{video} (大小: {video_size} 字节, 时长: {video_duration} 秒)")

        for processed in processed_videos:
            # 检查已处理视频是否为短视频
            is_processed_short = "_short" in processed
            
            # 短视频只与短视频比较，普通视频只与普通视频比较
            if is_current_short != is_processed_short:
                continue
                
            # 首先检查名称相似度
            similarity = fuzz.partial_ratio(video, processed)
            
            if similarity >= threshold:
                # 如果名称相似，再检查文件大小和时长
                processed_path = os.path.join(video_folder, f"{processed}.mp4")
                processed_size = get_file_size(processed_path)
                processed_duration = get_video_duration(processed_path)
                
                # 检查文件大小是否相似
                size_similar = is_size_similar(video_size, processed_size, size_tolerance)
                
                # 检查视频时长是否相似
                duration_similar = is_duration_similar(video_duration, processed_duration, duration_tolerance)
                
                print(f"对比: {video} 和 {processed}")
                print(f"  - 名称相似度: {similarity}%")
                print(f"  - 大小对比: {video_size} vs {processed_size} 字节")
                print(f"  - 大小相似: {size_similar}")
                print(f"  - 时长对比: {video_duration} vs {processed_duration} 秒")
                print(f"  - 时长相似: {duration_similar}")
                
                if size_similar and duration_similar:
                    print(f"匹配成功：名称相似且大小接近且时长接近: {video} 和 {processed}")
                    is_duplicate = True
                    break
                else:
                    print(f"名称相似但大小差异较大或时长差异较大，不视为重复: {video} 和 {processed}")
            
        if not is_duplicate:
            filtered_videos.append(video)
            print(f"保留未匹配视频：{video}")

    print(f"过滤后的待上传视频：{filtered_videos}")
    return filtered_videos

if __name__ == "__main__":
    logger.warning("脚本已启动。")
    start_video_monitoring()
