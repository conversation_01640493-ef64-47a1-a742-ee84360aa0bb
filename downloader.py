import os
import subprocess
import time
import random
from config import logger, USE_PROXY, PROXY_URL
from utils import save_downloaded_history, is_video_in_history, save_timestamp_info
from video_info import is_video_live
from translation import translate_title_with_retry
from thumbnail_downloader import download_thumbnail

def download_video(video_id, video_title, channel_name, download_location, upload_date):
    """下载YouTube视频 - 无字幕版本，按照封面→视频的顺序处理"""
    logger.warning(f"开始处理视频 - 上传日期: {upload_date}")

    # 检查是否已下载
    if is_video_in_history(video_id):
        logger.warning(f"视频已存在，跳过下载: [{channel_name}] {video_title}")
        return False

    # 检查是否在直播或预告中 (重要：放在最前面)
    video_url = f'https://www.youtube.com/watch?v={video_id}'
    logger.warning("检查视频是否是直播或直播预告...")
    
    live_status = is_video_live(video_url)
    if live_status:
        logger.warning(f"===检测到视频是直播或预告，完全跳过下载===: [{channel_name}] {video_title}")
        return False
    
    logger.warning("确认视频不是直播或预告，继续下载流程...")

    # 确定保存路径
    base_path = download_location
    os.makedirs(base_path, exist_ok=True)

    # 翻译标题
    translated_title = translate_title_with_retry(video_title)
    sanitized_title_full = translated_title
    sanitized_title_short = translated_title[:30]

    # 生成视频文件名
    filename = f'【{channel_name}】{sanitized_title_short}_{upload_date.strftime("%Y-%m-%d")}.mp4'
    output_path = os.path.join(base_path, filename)

    logger.warning(f"视频标题: {video_title}")
    logger.warning(f"翻译标题: {translated_title}")
    logger.warning(f"视频保存路径: {output_path}")

    # 步骤1: 下载封面
    logger.warning("第1步: 开始下载视频封面...")
    thumbnail_path = download_thumbnail(video_id, channel_name, sanitized_title_short)
    
    if not thumbnail_path:
        logger.warning("封面下载失败，但仍继续下载视频...")
    else:
        logger.warning(f"封面下载成功: {thumbnail_path}")
    
    # 步骤2: 提取时间戳信息
    logger.warning("第2步: 提取视频时间戳信息...")
    timestamp_info = extract_video_timestamp(video_url)
    
    if timestamp_info:
        logger.warning(f"成功提取时间戳信息: {timestamp_info}")
        # 保存时间戳信息到文件
        timestamp_file = save_timestamp_info(
            video_id, channel_name, sanitized_title_full, timestamp_info
        )
        logger.warning(f"时间戳信息已保存到: {timestamp_file}")
    else:
        logger.warning("无法提取时间戳信息")
        
    # 步骤3: 下载视频
    logger.warning("第3步: 开始下载视频...")

    # 使用增强的下载函数，包含重试机制和网络优化
    success = download_video_with_retry(video_url, output_path, max_retries=3)

    if success:
        logger.warning(f"成功下载视频：{video_title}")
        # 无字幕版本不传递字幕路径
        save_downloaded_history(video_id, channel_name, video_title,
                               None, thumbnail_path, timestamp_info)
        return True
    else:
        logger.error(f"视频下载失败，已尝试所有重试机会: {video_title}")
        return False

def download_video_with_retry(video_url, output_path, max_retries=3):
    """
    增强的视频下载函数，包含重试机制和网络优化
    """
    for attempt in range(max_retries):
        logger.warning(f"下载尝试 {attempt + 1}/{max_retries}")

        # 构建下载命令，添加网络优化参数
        command = build_download_command(video_url, output_path, attempt)

        try:
            logger.warning(f"执行下载命令: {' '.join(command)}")
            result = subprocess.run(command, capture_output=True, text=True, timeout=1800)  # 30分钟超时

            if result.returncode == 0:
                logger.warning(f"下载成功！")
                return True
            else:
                error_msg = result.stderr.strip()
                logger.error(f"下载失败，返回码: {result.returncode}")
                logger.error(f"错误信息: {error_msg}")

                # 分析错误类型
                if is_network_error(error_msg):
                    logger.warning(f"检测到网络错误，将在 {get_retry_delay(attempt)} 秒后重试...")
                    if attempt < max_retries - 1:  # 不是最后一次尝试
                        time.sleep(get_retry_delay(attempt))
                        continue
                else:
                    logger.error("检测到非网络错误，停止重试")
                    return False

        except subprocess.TimeoutExpired:
            logger.error(f"下载超时（30分钟），尝试 {attempt + 1} 失败")
            if attempt < max_retries - 1:
                logger.warning(f"将在 {get_retry_delay(attempt)} 秒后重试...")
                time.sleep(get_retry_delay(attempt))
                continue
        except Exception as e:
            logger.error(f"下载过程中出现异常: {e}")
            if attempt < max_retries - 1:
                logger.warning(f"将在 {get_retry_delay(attempt)} 秒后重试...")
                time.sleep(get_retry_delay(attempt))
                continue

    logger.error(f"所有 {max_retries} 次下载尝试均失败")
    return False

def build_download_command(video_url, output_path, attempt):
    """
    构建下载命令，根据尝试次数调整参数
    """
    command = ['yt-dlp']

    # 基础参数
    command.extend(['--cookies-from-browser', 'firefox'])

    # 代理设置
    if USE_PROXY:
        command.extend(['--proxy', PROXY_URL])

    # 根据尝试次数调整网络参数
    if attempt == 0:
        # 第一次尝试：标准参数
        command.extend([
            '--socket-timeout', '60',
            '--retries', '3'
        ])
    elif attempt == 1:
        # 第二次尝试：增加超时时间，降低质量
        command.extend([
            '--socket-timeout', '120',
            '--retries', '5',
            '--fragment-retries', '10'
        ])
    else:
        # 第三次尝试：最保守的设置
        command.extend([
            '--socket-timeout', '180',
            '--retries', '10',
            '--fragment-retries', '20',
            '--throttled-rate', '100K'  # 限制下载速度
        ])

    # 输出格式和质量设置
    command.extend([
        '--merge-output-format', 'mp4',
        '--format', 'bestvideo[height<=720]+bestaudio/best' if attempt < 2 else 'best[height<=480]',  # 第三次尝试降低质量
        '-o', output_path,
        video_url
    ])

    return command

def is_network_error(error_msg):
    """
    判断是否为网络相关错误
    """
    network_error_keywords = [
        'Connection to tcp://',
        'Error number -138',
        'Error opening input',
        'manifest.googlevideo.com',
        'Connection refused',
        'Connection timed out',
        'Network is unreachable',
        'Temporary failure in name resolution',
        'Unable to connect',
        'HTTP Error 5',  # 5xx 服务器错误
        'Read timed out',
        'Connection reset by peer'
    ]

    return any(keyword in error_msg for keyword in network_error_keywords)

def get_retry_delay(attempt):
    """
    获取重试延迟时间（指数退避 + 随机抖动）
    """
    base_delay = 30  # 基础延迟30秒
    exponential_delay = base_delay * (2 ** attempt)  # 指数退避
    jitter = random.uniform(0.5, 1.5)  # 随机抖动
    return int(exponential_delay * jitter)

def extract_video_timestamp(video_url):
    """提取视频的时间戳信息(时长等)"""
    try:
        command = [
            'yt-dlp',
            '--cookies-from-browser', 'firefox',
        ]
        if USE_PROXY:
            command += ['--proxy', PROXY_URL]
        command += [
            '--skip-download',
            '--print', '%(duration)s,%(upload_date)s,%(view_count)s',
            video_url
        ]
        
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            # 解析输出: 持续时间(秒),上传日期,观看次数
            info_parts = result.stdout.strip().split(',')
            if len(info_parts) >= 3:
                duration_seconds = int(info_parts[0]) if info_parts[0].strip().isdigit() else 0
                upload_date = info_parts[1].strip()
                view_count = info_parts[2].strip()
                
                # 将秒转换为时:分:秒格式
                hours = duration_seconds // 3600
                minutes = (duration_seconds % 3600) // 60
                seconds = duration_seconds % 60
                duration_formatted = f"{hours:02}:{minutes:02}:{seconds:02}"
                
                return {
                    "duration_seconds": duration_seconds,
                    "duration_formatted": duration_formatted,
                    "upload_date": upload_date,
                    "view_count": view_count
                }
        
        logger.error(f"无法提取时间戳信息，yt-dlp输出: {result.stdout}")
        return None
    except Exception as e:
        logger.error(f"提取时间戳时出错: {e}")
        return None
