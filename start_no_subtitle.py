#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无字幕版本启动脚本
"""

import sys
import os

def main():
    """启动无字幕版本的YouTube下载系统"""
    print("=" * 60)
    print("YouTube视频下载系统 - 无字幕版本")
    print("=" * 60)
    print("特点:")
    print("✅ 下载视频文件")
    print("✅ 下载视频封面")
    print("✅ 翻译视频标题")
    print("✅ 提取时间戳信息")
    print("❌ 不下载字幕文件")
    print("=" * 60)
    
    try:
        # 导入并启动主程序
        from main import start_video_monitoring
        from config import logger
        
        logger.warning("无字幕版本启动中...")
        start_video_monitoring()
        
    except KeyboardInterrupt:
        print("\n用户中断，程序退出。")
        return 0
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
