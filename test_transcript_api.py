#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 youtube-transcript-api 工具
"""

from youtube_transcript_api import YouTubeTranscriptApi
from config import logger
import os

def test_transcript_api(video_id, chinese_title, channel_name):
    """
    测试使用 youtube-transcript-api 下载字幕
    """
    logger.warning(f"测试 youtube-transcript-api 下载字幕")
    logger.warning(f"视频ID: {video_id}")
    logger.warning(f"标题: {chinese_title}")
    
    try:
        # 获取可用的字幕语言列表
        logger.warning("获取可用字幕语言列表...")
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        available_languages = []
        for transcript in transcript_list:
            available_languages.append({
                'language': transcript.language,
                'language_code': transcript.language_code,
                'is_generated': transcript.is_generated,
                'is_translatable': transcript.is_translatable
            })
            logger.warning(f"可用语言: {transcript.language} ({transcript.language_code}) - 自动生成: {transcript.is_generated}")
        
        # 尝试获取中文字幕
        chinese_codes = ['zh-Hans', 'zh-Hant', 'zh', 'zh-CN', 'zh-TW']
        transcript_data = None
        used_language = None
        
        for lang_code in chinese_codes:
            try:
                logger.warning(f"尝试获取 {lang_code} 字幕...")
                transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang_code])
                used_language = lang_code
                logger.warning(f"✅ 成功获取 {lang_code} 字幕，共 {len(transcript_data)} 条记录")
                break
            except Exception as e:
                logger.warning(f"❌ {lang_code} 字幕不可用: {e}")
                continue
        
        # 如果没有中文字幕，尝试翻译英文字幕
        if not transcript_data:
            try:
                logger.warning("尝试获取英文字幕并翻译为中文...")
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                
                # 获取英文字幕
                english_transcript = transcript_list.find_transcript(['en'])
                # 翻译为中文
                translated_transcript = english_transcript.translate('zh-Hans')
                transcript_data = translated_transcript.fetch()
                used_language = 'zh-Hans (translated from en)'
                logger.warning(f"✅ 成功翻译英文字幕为中文，共 {len(transcript_data)} 条记录")
                
            except Exception as e:
                logger.error(f"❌ 翻译英文字幕失败: {e}")
                return False
        
        if transcript_data:
            # 转换为SRT格式
            srt_content = convert_transcript_to_srt(transcript_data)
            
            # 保存SRT文件
            srt_filename = f"【{channel_name}】{chinese_title}.zh-Hans.srt"
            srt_path = f"D:/ytb_python_download/srt_han/{srt_filename}"
            
            with open(srt_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            logger.warning(f"✅ 字幕保存成功: {srt_path}")
            logger.warning(f"使用语言: {used_language}")
            logger.warning(f"文件大小: {os.path.getsize(srt_path)} bytes")
            
            return True
        else:
            logger.error("❌ 无法获取任何字幕")
            return False
            
    except Exception as e:
        logger.error(f"❌ youtube-transcript-api 下载失败: {e}")
        return False

def convert_transcript_to_srt(transcript_data):
    """
    将 transcript 数据转换为 SRT 格式
    """
    srt_content = []
    
    for i, entry in enumerate(transcript_data, 1):
        start_time = entry['start']
        duration = entry['duration']
        end_time = start_time + duration
        text = entry['text'].strip()
        
        # 转换时间格式
        start_srt = seconds_to_srt_time(start_time)
        end_srt = seconds_to_srt_time(end_time)
        
        # 添加SRT条目
        srt_content.append(str(i))
        srt_content.append(f"{start_srt} --> {end_srt}")
        srt_content.append(text)
        srt_content.append("")  # 空行
    
    return "\n".join(srt_content)

def seconds_to_srt_time(seconds):
    """
    将秒数转换为SRT时间格式 (HH:MM:SS,mmm)
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def main():
    """
    测试主函数
    """
    # 测试视频信息
    test_video = {
        "video_id": "VSRRsAEtymE",
        "chinese_title": "选择：未来48-72小时能量状态查看 🪄接下来会发生什么💫",
        "channel_name": "222"
    }
    
    print("=" * 60)
    print("测试 youtube-transcript-api 字幕下载")
    print("=" * 60)
    
    success = test_transcript_api(
        test_video["video_id"],
        test_video["chinese_title"],
        test_video["channel_name"]
    )
    
    if success:
        print("✅ youtube-transcript-api 测试成功！")
    else:
        print("❌ youtube-transcript-api 测试失败")

if __name__ == "__main__":
    main()
