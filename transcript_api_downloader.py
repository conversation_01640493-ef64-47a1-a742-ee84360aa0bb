#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 youtube-transcript-api 的字幕下载器
"""

from youtube_transcript_api import YouTubeTranscriptApi
from config import logger
import os
from utils import sanitize_filename

def download_subtitle_with_transcript_api(video_id, chinese_title, channel_name):
    """
    使用 youtube-transcript-api 下载字幕
    """
    logger.warning(f"使用 youtube-transcript-api 下载字幕: {chinese_title}")
    
    try:
        # 构建文件路径
        clean_title = sanitize_filename(chinese_title)
        srt_filename = f"【{channel_name}】{clean_title}.zh-Hans.srt"
        srt_path = f"D:/ytb_python_download/srt_han/{srt_filename}"
        
        # 如果文件已存在，跳过
        if os.path.exists(srt_path):
            logger.warning(f"✅ 字幕文件已存在，跳过: {srt_filename}")
            return True
        
        # 获取字幕列表
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        # 尝试直接获取中文字幕
        transcript_data = None
        used_method = None
        
        for lang_code in ['zh-Hans', 'zh-Hant', 'zh']:
            try:
                transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang_code])
                used_method = f"直接获取 {lang_code}"
                logger.warning(f"✅ {used_method} 成功，共 {len(transcript_data)} 条")
                break
            except:
                continue
        
        # 如果没有中文字幕，尝试翻译英文字幕
        if not transcript_data:
            try:
                logger.warning("尝试翻译英文字幕为中文...")
                
                # 获取英文字幕并翻译
                english_transcript = transcript_list.find_transcript(['en'])
                translated_transcript = english_transcript.translate('zh-Hans')
                transcript_data = translated_transcript.fetch()
                used_method = "英文翻译为中文"
                logger.warning(f"✅ {used_method} 成功，共 {len(transcript_data)} 条")
                
            except Exception as e:
                logger.error(f"❌ 翻译失败: {e}")
                return False
        
        if transcript_data:
            # 转换为SRT格式
            srt_content = convert_transcript_to_srt(transcript_data)
            
            # 保存文件
            with open(srt_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)
            
            file_size = os.path.getsize(srt_path)
            logger.warning(f"✅ 字幕保存成功: {srt_filename}")
            logger.warning(f"方法: {used_method}, 大小: {file_size} bytes")
            
            return True
        else:
            logger.error("❌ 无法获取字幕数据")
            return False
            
    except Exception as e:
        logger.error(f"❌ youtube-transcript-api 下载失败: {e}")
        return False

def convert_transcript_to_srt(transcript_data):
    """
    将 transcript 数据转换为 SRT 格式
    """
    srt_lines = []
    
    for i, entry in enumerate(transcript_data, 1):
        start_time = entry['start']
        duration = entry.get('duration', 3.0)  # 默认3秒
        end_time = start_time + duration
        text = entry['text'].strip()
        
        # 跳过空文本
        if not text:
            continue
        
        # 转换时间格式
        start_srt = seconds_to_srt_time(start_time)
        end_srt = seconds_to_srt_time(end_time)
        
        # 添加SRT条目
        srt_lines.append(str(i))
        srt_lines.append(f"{start_srt} --> {end_srt}")
        srt_lines.append(text)
        srt_lines.append("")  # 空行
    
    return "\n".join(srt_lines)

def seconds_to_srt_time(seconds):
    """
    将秒数转换为SRT时间格式 (HH:MM:SS,mmm)
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def test_transcript_api():
    """
    测试函数
    """
    test_video = {
        "video_id": "_fZZsjMvUTk",  # 测试新视频
        "chinese_title": "🎭有人对你隐瞒着重要的事情……塔罗牌占卜",
        "channel_name": "313"
    }
    
    print("=" * 60)
    print("测试 youtube-transcript-api 字幕下载")
    print("=" * 60)
    
    success = download_subtitle_with_transcript_api(
        test_video["video_id"],
        test_video["chinese_title"],
        test_video["channel_name"]
    )
    
    if success:
        print("✅ youtube-transcript-api 测试成功！")
    else:
        print("❌ youtube-transcript-api 测试失败")

if __name__ == "__main__":
    test_transcript_api()
