#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 youtube-transcript-api
"""

from youtube_transcript_api import YouTubeTranscriptApi

def simple_test():
    video_id = "VSRRsAEtymE"
    
    print(f"测试视频ID: {video_id}")
    
    try:
        # 获取可用字幕列表
        print("获取可用字幕列表...")
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        print("可用字幕:")
        for transcript in transcript_list:
            print(f"- {transcript.language} ({transcript.language_code}) - 自动生成: {transcript.is_generated}")
        
        # 尝试获取中文字幕
        print("\n尝试获取中文字幕...")
        try:
            transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['zh-Hans', 'zh'])
            print(f"✅ 成功获取中文字幕，共 {len(transcript)} 条")
            
            # 显示前3条
            for i, entry in enumerate(transcript[:3]):
                print(f"{i+1}. {entry['start']:.2f}s: {entry['text']}")
                
        except Exception as e:
            print(f"❌ 中文字幕获取失败: {e}")
            
            # 尝试英文字幕
            try:
                print("尝试获取英文字幕...")
                transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
                print(f"✅ 成功获取英文字幕，共 {len(transcript)} 条")
                
                # 显示前3条
                for i, entry in enumerate(transcript[:3]):
                    print(f"{i+1}. {entry['start']:.2f}s: {entry['text']}")
                    
            except Exception as e2:
                print(f"❌ 英文字幕也获取失败: {e2}")
        
    except Exception as e:
        print(f"❌ 总体测试失败: {e}")

if __name__ == "__main__":
    simple_test()
