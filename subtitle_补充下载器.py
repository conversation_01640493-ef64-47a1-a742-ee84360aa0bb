#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无字幕版本 - 字幕补充下载器
为已下载的视频批量补充下载中文字幕
"""

import os
import sys
import subprocess
from config import logger, HISTORY_VIDEOS_FILE, USE_PROXY, PROXY_URL
from utils import sanitize_filename

def parse_history():
    """
    解析历史记录，支持新旧两种格式
    新格式: 下载时间 | video_id | 频道名 | 中文标题 | 字幕状态
    旧格式: video_id - [channel_name] 中文标题 | 其他信息...

    返回: [(video_id, chinese_title, clean_title, channel_name), ...]
    """
    videos_info = []

    if not os.path.exists(HISTORY_VIDEOS_FILE):
        logger.error(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        return videos_info

    logger.warning("开始解析下载历史记录...")

    with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            try:
                # 新格式：下载时间 | video_id | 频道名 | 中文标题 | 字幕状态
                if ' | ' in line and line.startswith(('2024-', '2025-')):
                    parts = line.split(' | ')
                    if len(parts) >= 4:
                        video_id = parts[1].strip()
                        channel_name = parts[2].strip()
                        chinese_title = parts[3].strip()

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                        continue

                # 旧格式处理
                if ' - ' not in line:
                    continue

                # 包含封面和时长信息的旧格式：video_id - [channel] title | 封面: ... | 时长: ...
                if ' | ' in line and not line.startswith(('2024-', '2025-')):
                    parts = line.split(' - ', 1)
                    if len(parts) < 2:
                        continue

                    video_id = parts[0].strip()
                    remaining = parts[1]

                    # 提取频道名和标题
                    if remaining.startswith('[') and ']' in remaining:
                        end_bracket = remaining.find(']')
                        channel_name = remaining[1:end_bracket]

                        # 提取中文标题（去掉封面和时长信息）
                        title_part = remaining[end_bracket + 1:].strip()
                        if ' | ' in title_part:
                            chinese_title = title_part.split(' | ')[0].strip()
                        else:
                            chinese_title = title_part

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                        continue

                # 简单旧格式：video_id - [channel_name] 中文标题
                elif ' - ' in line:
                    parts = line.split(' - ', 1)
                    if len(parts) < 2:
                        continue

                    video_id = parts[0].strip()
                    remaining = parts[1]

                    # 提取频道名和标题
                    if remaining.startswith('[') and ']' in remaining:
                        end_bracket = remaining.find(']')
                        channel_name = remaining[1:end_bracket]

                        # 提取中文标题
                        chinese_title = remaining[end_bracket + 1:].strip()

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                    else:
                        logger.warning(f"跳过格式不正确的行: {line[:50]}...")

            except Exception as e:
                logger.error(f"解析第 {line_num} 行时出错: {line[:50]}... 错误: {e}")
                continue

    logger.warning(f"成功解析了 {len(videos_info)} 个视频记录")
    return videos_info

def check_subtitle_exists(clean_title):
    """
    检查字幕文件是否已存在
    """
    # 检查SRT文件
    srt_path = f"../srt_han/{clean_title}.zh-Hans.srt"
    vtt_path = f"../vtt_han/{clean_title}.zh-Hans.vtt"
    
    return os.path.exists(srt_path) or os.path.exists(vtt_path)

def download_subtitle_only(video_id, chinese_title, clean_title):
    """
    只下载字幕，不下载视频
    """
    video_url = f'https://www.youtube.com/watch?v={video_id}'
    
    logger.warning(f"开始下载字幕: {chinese_title}")
    logger.warning(f"视频URL: {video_url}")
    
    # 构建yt-dlp命令，只下载字幕
    command = [
        'yt-dlp',
        '--cookies-from-browser', 'firefox',
        '--write-subs',
        '--write-auto-subs',
        '--sub-langs', 'zh-Hans,zh-Hant,zh,en',
        '--skip-download',  # 跳过视频下载，只下载字幕
        '--sub-format', 'srt/vtt',
        '-o', f'../srt_han/{clean_title}.%(ext)s',
        video_url
    ]
    
    if USE_PROXY:
        command.insert(2, '--proxy')
        command.insert(3, PROXY_URL)
    
    try:
        logger.warning(f"执行字幕下载命令: {' '.join(command)}")
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logger.warning(f"✅ 字幕下载成功: {chinese_title}")
            return True
        else:
            logger.error(f"❌ 字幕下载失败: {chinese_title}")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error(f"❌ 字幕下载超时: {chinese_title}")
        return False
    except Exception as e:
        logger.error(f"❌ 字幕下载异常: {chinese_title} - {e}")
        return False

def batch_download_subtitles_for_history(max_count=None, start_from=0):
    """
    为历史记录中的所有视频批量下载字幕
    
    Args:
        max_count: 最大处理数量，None表示处理所有
        start_from: 从第N个开始处理（用于断点续传）
    """
    logger.warning("=" * 60)
    logger.warning("开始为历史记录批量下载字幕")
    logger.warning("=" * 60)
    
    # 解析历史记录
    videos_info = parse_old_format_history()
    
    if not videos_info:
        logger.error("没有找到任何视频记录")
        return
    
    # 应用起始位置和数量限制
    if start_from > 0:
        videos_info = videos_info[start_from:]
        logger.warning(f"从第 {start_from + 1} 个视频开始处理")
    
    if max_count:
        videos_info = videos_info[:max_count]
        logger.warning(f"限制处理数量: {max_count}")
    
    # 统计信息
    total_count = len(videos_info)
    success_count = 0
    skip_count = 0
    fail_count = 0
    
    logger.warning(f"准备处理 {total_count} 个视频的字幕下载")
    
    for i, (video_id, chinese_title, clean_title, channel_name) in enumerate(videos_info, 1):
        current_index = start_from + i
        logger.warning(f"\n进度: {i}/{total_count} (总进度: {current_index})")
        logger.warning(f"处理视频: {video_id}")
        logger.warning(f"频道: [{channel_name}]")
        logger.warning(f"标题: {chinese_title}")
        
        # 检查是否已有字幕
        if check_subtitle_exists(clean_title):
            logger.warning(f"⏭️  跳过（已有字幕）: {chinese_title}")
            skip_count += 1
            continue
        
        # 下载字幕
        if download_subtitle_only(video_id, chinese_title, clean_title):
            success_count += 1
        else:
            fail_count += 1
        
        # 每10个视频显示一次进度
        if i % 10 == 0:
            logger.warning(f"📊 当前进度: 成功 {success_count}, 跳过 {skip_count}, 失败 {fail_count}")
    
    # 输出最终统计结果
    logger.warning("\n" + "=" * 60)
    logger.warning("批量字幕下载完成")
    logger.warning("=" * 60)
    logger.warning(f"总计处理: {total_count} 个视频")
    logger.warning(f"成功下载: {success_count} 个字幕")
    logger.warning(f"跳过处理: {skip_count} 个（已有字幕）")
    logger.warning(f"下载失败: {fail_count} 个")
    logger.warning(f"成功率: {success_count/(total_count-skip_count)*100:.1f}%" if total_count > skip_count else "N/A")
    logger.warning("=" * 60)

def main():
    """
    主函数 - 直接测试字幕补充下载功能
    """
    print("=" * 60)
    print("无字幕版本 - 字幕补充下载器测试")
    print("直接处理前3个视频进行测试")
    print("=" * 60)

    # 直接开始批量下载，限制处理前3个视频进行测试
    batch_download_subtitles_for_history(max_count=3, start_from=0)

if __name__ == "__main__":
    main()
