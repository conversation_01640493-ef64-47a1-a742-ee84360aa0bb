#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无字幕版本 - 字幕补充下载器
为已下载的视频批量补充下载中文字幕
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from config import logger, HISTORY_VIDEOS_FILE, USE_PROXY, PROXY_URL
from utils import sanitize_filename

# 全局变量：记录已处理的video_id
processed_videos = set()
log_file_path = "subtitle_download_log.txt"

def log_to_file(message):
    """将消息同时输出到控制台和文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_message = f"[{timestamp}] {message}"

    # 输出到控制台
    print(log_message)

    # 保存到文件
    try:
        with open(log_file_path, 'a', encoding='utf-8') as f:
            f.write(log_message + "\n")
    except Exception as e:
        print(f"写入日志文件失败: {e}")

def load_processed_videos():
    """加载已处理的video_id列表"""
    global processed_videos
    processed_file = "processed_subtitle_videos.txt"

    if os.path.exists(processed_file):
        try:
            with open(processed_file, 'r', encoding='utf-8') as f:
                for line in f:
                    video_id = line.strip()
                    if video_id:
                        processed_videos.add(video_id)
            log_to_file(f"加载了 {len(processed_videos)} 个已处理的video_id")
        except Exception as e:
            log_to_file(f"加载已处理列表失败: {e}")

def save_processed_video(video_id):
    """保存已处理的video_id"""
    global processed_videos
    processed_file = "processed_subtitle_videos.txt"

    if video_id not in processed_videos:
        processed_videos.add(video_id)
        try:
            with open(processed_file, 'a', encoding='utf-8') as f:
                f.write(video_id + "\n")
        except Exception as e:
            log_to_file(f"保存已处理列表失败: {e}")

def parse_history():
    """
    解析历史记录，支持新旧两种格式
    新格式: 下载时间 | video_id | 频道名 | 中文标题 | 字幕状态
    旧格式: video_id - [channel_name] 中文标题 | 其他信息...

    返回: [(video_id, chinese_title, clean_title, channel_name), ...]
    """
    videos_info = []

    if not os.path.exists(HISTORY_VIDEOS_FILE):
        logger.error(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        return videos_info

    logger.warning("开始解析下载历史记录...")

    with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            try:
                # 新格式：下载时间 | video_id | 频道名 | 中文标题 | 字幕状态
                if ' | ' in line and line.startswith(('2024-', '2025-')):
                    parts = line.split(' | ')
                    if len(parts) >= 4:
                        video_id = parts[1].strip()
                        channel_name = parts[2].strip()
                        chinese_title = parts[3].strip()

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                        continue

                # 旧格式处理
                if ' - ' not in line:
                    continue

                # 包含封面和时长信息的旧格式：video_id - [channel] title | 封面: ... | 时长: ...
                if ' | ' in line and not line.startswith(('2024-', '2025-')):
                    parts = line.split(' - ', 1)
                    if len(parts) < 2:
                        continue

                    video_id = parts[0].strip()
                    remaining = parts[1]

                    # 提取频道名和标题
                    if remaining.startswith('[') and ']' in remaining:
                        end_bracket = remaining.find(']')
                        channel_name = remaining[1:end_bracket]

                        # 提取中文标题（去掉封面和时长信息）
                        title_part = remaining[end_bracket + 1:].strip()
                        if ' | ' in title_part:
                            chinese_title = title_part.split(' | ')[0].strip()
                        else:
                            chinese_title = title_part

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                        continue

                # 简单旧格式：video_id - [channel_name] 中文标题
                elif ' - ' in line:
                    parts = line.split(' - ', 1)
                    if len(parts) < 2:
                        continue

                    video_id = parts[0].strip()
                    remaining = parts[1]

                    # 提取频道名和标题
                    if remaining.startswith('[') and ']' in remaining:
                        end_bracket = remaining.find(']')
                        channel_name = remaining[1:end_bracket]

                        # 提取中文标题
                        chinese_title = remaining[end_bracket + 1:].strip()

                        # 清理标题，用于文件命名
                        clean_title = sanitize_filename(chinese_title)
                        videos_info.append((video_id, chinese_title, clean_title, channel_name))
                    else:
                        logger.warning(f"跳过格式不正确的行: {line[:50]}...")

            except Exception as e:
                logger.error(f"解析第 {line_num} 行时出错: {line[:50]}... 错误: {e}")
                continue

    logger.warning(f"成功解析了 {len(videos_info)} 个视频记录")
    return videos_info

def check_subtitle_exists(clean_title):
    """
    检查字幕文件是否已存在
    """
    # 检查SRT文件
    srt_path = f"../srt_han/{clean_title}.zh-Hans.srt"
    vtt_path = f"../vtt_han/{clean_title}.zh-Hans.vtt"
    
    return os.path.exists(srt_path) or os.path.exists(vtt_path)

def download_subtitle_only(video_id, title, clean_title, channel_name):
    """
    只下载字幕，不下载视频
    直接使用历史记录中的标题，不进行翻译
    """
    video_url = f'https://www.youtube.com/watch?v={video_id}'

    log_to_file(f"开始下载字幕: [{channel_name}] {title}")
    log_to_file(f"视频ID: {video_id}")

    # 构建yt-dlp命令，只下载字幕
    command = [
        'yt-dlp',
        '--cookies-from-browser', 'firefox',
        '--write-subs',
        '--write-auto-subs',
        '--sub-langs', 'zh-Hans,zh-Hant,zh,en',
        '--skip-download',  # 跳过视频下载，只下载字幕
        '--sub-format', 'srt/vtt',
        '-o', f'../srt_han/【{channel_name}】{clean_title}.%(ext)s',
        video_url
    ]

    if USE_PROXY:
        command.insert(2, '--proxy')
        command.insert(3, PROXY_URL)

    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            log_to_file(f"✅ 字幕下载成功: {title}")
            save_processed_video(video_id)
            return True
        else:
            error_msg = result.stderr
            if "HTTP Error 429" in error_msg:
                log_to_file(f"❌ 请求过于频繁: {title}")
                log_to_file("等待60秒后继续...")
                time.sleep(60)
                return False
            else:
                log_to_file(f"❌ 字幕下载失败: {title}")
                log_to_file(f"错误信息: {error_msg}")
                save_processed_video(video_id)  # 标记为已处理，避免重复尝试
                return False

    except subprocess.TimeoutExpired:
        log_to_file(f"❌ 字幕下载超时: {title}")
        save_processed_video(video_id)
        return False
    except Exception as e:
        log_to_file(f"❌ 字幕下载异常: {title} - {e}")
        save_processed_video(video_id)
        return False

def batch_download_subtitles_for_history(max_count=None, start_from=0):
    """
    为历史记录中的所有视频批量下载字幕
    
    Args:
        max_count: 最大处理数量，None表示处理所有
        start_from: 从第N个开始处理（用于断点续传）
    """
    logger.warning("=" * 60)
    logger.warning("开始为历史记录批量下载字幕")
    logger.warning("=" * 60)
    
    # 解析历史记录
    videos_info = parse_history()
    
    if not videos_info:
        logger.error("没有找到任何视频记录")
        return
    
    # 应用起始位置和数量限制
    if start_from > 0:
        videos_info = videos_info[start_from:]
        logger.warning(f"从第 {start_from + 1} 个视频开始处理")
    
    if max_count:
        videos_info = videos_info[:max_count]
        logger.warning(f"限制处理数量: {max_count}")
    
    # 统计信息
    total_count = len(videos_info)
    success_count = 0
    skip_count = 0
    fail_count = 0
    
    logger.warning(f"准备处理 {total_count} 个视频的字幕下载")
    
    for i, (video_id, chinese_title, clean_title, channel_name) in enumerate(videos_info, 1):
        current_index = start_from + i
        logger.warning(f"\n进度: {i}/{total_count} (总进度: {current_index})")
        logger.warning(f"处理视频: {video_id}")
        logger.warning(f"频道: [{channel_name}]")
        logger.warning(f"标题: {chinese_title}")
        
        # 检查是否已有字幕
        if check_subtitle_exists(clean_title):
            logger.warning(f"⏭️  跳过（已有字幕）: {chinese_title}")
            skip_count += 1
            continue
        
        # 下载字幕
        if download_subtitle_only(video_id, chinese_title, clean_title, channel_name):
            success_count += 1
        else:
            fail_count += 1

        # 添加延迟避免请求过于频繁
        logger.warning("等待15秒后处理下一个视频...")
        time.sleep(15)
        
        # 每10个视频显示一次进度
        if i % 10 == 0:
            logger.warning(f"📊 当前进度: 成功 {success_count}, 跳过 {skip_count}, 失败 {fail_count}")
    
    # 输出最终统计结果
    logger.warning("\n" + "=" * 60)
    logger.warning("批量字幕下载完成")
    logger.warning("=" * 60)
    logger.warning(f"总计处理: {total_count} 个视频")
    logger.warning(f"成功下载: {success_count} 个字幕")
    logger.warning(f"跳过处理: {skip_count} 个（已有字幕）")
    logger.warning(f"下载失败: {fail_count} 个")
    logger.warning(f"成功率: {success_count/(total_count-skip_count)*100:.1f}%" if total_count > skip_count else "N/A")
    logger.warning("=" * 60)

def continuous_subtitle_monitor():
    """
    持续监控并下载缺失的字幕
    """
    log_to_file("=" * 60)
    log_to_file("启动字幕补充下载器 - 持续监控模式")
    log_to_file("=" * 60)

    # 加载已处理的video_id
    load_processed_videos()

    check_interval = 300  # 5分钟检查一次

    while True:
        try:
            log_to_file("开始检查缺失的字幕...")

            # 解析历史记录
            videos_info = parse_history()
            if not videos_info:
                log_to_file("没有找到任何视频记录，等待下次检查...")
                time.sleep(check_interval)
                continue

            # 找到所有缺失字幕且未处理过的视频
            missing_subtitles = []
            for video_id, title, clean_title, channel_name in videos_info:
                # 跳过已处理的视频
                if video_id in processed_videos:
                    continue

                if not check_subtitle_exists(clean_title):
                    missing_subtitles.append((video_id, title, clean_title, channel_name))

            if not missing_subtitles:
                log_to_file("✅ 所有视频都已有字幕或已处理过，等待下次检查...")
                time.sleep(check_interval)
                continue

            log_to_file(f"发现 {len(missing_subtitles)} 个视频缺失字幕，开始下载...")

            # 下载缺失的字幕
            success_count = 0
            for i, (video_id, title, clean_title, channel_name) in enumerate(missing_subtitles, 1):
                log_to_file(f"进度: {i}/{len(missing_subtitles)}")

                if download_subtitle_only(video_id, title, clean_title, channel_name):
                    success_count += 1

                # 只在非429错误时等待5秒（429错误内部已经等待了60秒）
                if i < len(missing_subtitles):
                    log_to_file("等待5秒后处理下一个视频...")
                    time.sleep(5)

            log_to_file(f"本轮下载完成: 成功 {success_count}/{len(missing_subtitles)}")
            log_to_file(f"等待 {check_interval//60} 分钟后进行下次检查...")
            time.sleep(check_interval)

        except KeyboardInterrupt:
            log_to_file("用户中断，退出监控...")
            break
        except Exception as e:
            log_to_file(f"监控过程中发生错误: {e}")
            log_to_file("等待60秒后重试...")
            time.sleep(60)

def main():
    """
    主函数 - 字幕补充下载功能
    """
    print("=" * 60)
    print("无字幕版本 - 字幕补充下载器")
    print("持续监控模式 - 自动检测并下载缺失的字幕")
    print("=" * 60)

    # 启动持续监控
    continuous_subtitle_monitor()

if __name__ == "__main__":
    main()
