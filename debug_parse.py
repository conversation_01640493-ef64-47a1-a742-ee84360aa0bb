#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试解析功能
"""

import os
from config import logger, HISTORY_VIDEOS_FILE
from utils import sanitize_filename

def debug_parse_history():
    """
    调试解析历史记录
    """
    videos_info = []
    
    if not os.path.exists(HISTORY_VIDEOS_FILE):
        print(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        return videos_info
    
    print("开始调试解析历史记录...")
    
    with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#') or ' - ' not in line:
                print(f"跳过第 {line_num} 行: {line[:50]}...")
                continue
                
            try:
                # 解析格式: video_id - [channel_name] 中文标题 | 其他信息...
                parts = line.split(' - ', 1)
                if len(parts) < 2:
                    print(f"第 {line_num} 行格式错误，跳过: {line[:50]}...")
                    continue
                    
                video_id = parts[0].strip()
                remaining = parts[1]
                
                print(f"第 {line_num} 行:")
                print(f"  video_id: {video_id}")
                print(f"  remaining: {remaining[:100]}...")
                
                # 提取频道名和标题
                if remaining.startswith('[') and ']' in remaining:
                    # 找到频道名结束位置
                    end_bracket = remaining.find(']')
                    channel_name = remaining[1:end_bracket]
                    
                    # 提取中文标题（去掉可能的额外信息）
                    title_part = remaining[end_bracket + 1:].strip()
                    if ' | ' in title_part:
                        chinese_title = title_part.split(' | ')[0].strip()
                    else:
                        chinese_title = title_part
                    
                    # 清理标题，用于文件命名
                    clean_title = sanitize_filename(chinese_title)
                    
                    print(f"  channel_name: {channel_name}")
                    print(f"  chinese_title: {chinese_title}")
                    print(f"  clean_title: {clean_title}")
                    
                    videos_info.append((video_id, chinese_title, clean_title, channel_name))
                    
                    # 只处理前5个进行调试
                    if len(videos_info) >= 5:
                        break
                else:
                    print(f"  格式不正确，跳过")
                    
            except Exception as e:
                print(f"解析第 {line_num} 行时出错: {e}")
                print(f"  行内容: {line[:100]}...")
                continue
    
    print(f"\n成功解析了 {len(videos_info)} 个视频记录")
    return videos_info

if __name__ == "__main__":
    debug_parse_history()
