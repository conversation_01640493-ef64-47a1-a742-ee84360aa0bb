#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无字幕版本 - 字幕补充下载器
为已下载的视频批量补充下载中文字幕
"""

import os
import sys
import subprocess
from config import logger, HISTORY_VIDEOS_FILE, USE_PROXY, PROXY_URL
from utils import sanitize_filename

def parse_old_format_history():
    """
    解析旧格式的历史记录，提取video ID和中文标题
    从封面信息中提取已保存的中文标题
    格式: video_id - [channel_name] 英文标题 | 封面: 【频道名】中文标题_视频ID.jpg | 其他信息...

    返回: [(video_id, chinese_title, clean_title, channel_name), ...]
    """
    videos_info = []

    if not os.path.exists(HISTORY_VIDEOS_FILE):
        logger.error(f"未找到历史记录文件: {HISTORY_VIDEOS_FILE}")
        return videos_info

    logger.warning("开始解析旧格式下载历史记录...")

    with open(HISTORY_VIDEOS_FILE, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#') or ' - ' not in line:
                continue

            try:
                # 解析格式: video_id - [channel_name] 英文标题 | 封面: 【频道名】中文标题_视频ID.jpg | 其他信息...
                parts = line.split(' - ', 1)
                if len(parts) < 2:
                    continue

                video_id = parts[0].strip()
                remaining = parts[1]

                # 提取频道名
                if remaining.startswith('[') and ']' in remaining:
                    end_bracket = remaining.find(']')
                    channel_name = remaining[1:end_bracket]

                    # 查找封面信息中的中文标题
                    chinese_title = None
                    if '封面:' in remaining:
                        # 提取封面部分: 封面: 【频道名】中文标题_视频ID.jpg
                        cover_part = remaining.split('封面:')[1].split('|')[0].strip()

                        # 从封面文件名中提取中文标题
                        # 格式: 【频道名】中文标题_视频ID.jpg
                        if cover_part.startswith('【') and '】' in cover_part:
                            # 找到频道名结束位置
                            cover_end_bracket = cover_part.find('】')
                            # 提取中文标题部分（去掉_视频ID.jpg）
                            title_with_id = cover_part[cover_end_bracket + 1:]

                            # 去掉最后的_视频ID.jpg部分
                            if f'_{video_id}.jpg' in title_with_id:
                                chinese_title = title_with_id.replace(f'_{video_id}.jpg', '')
                            else:
                                # 如果没有找到标准格式，尝试其他方式
                                chinese_title = title_with_id.rsplit('_', 1)[0] if '_' in title_with_id else title_with_id

                    # 如果没有找到中文标题，使用英文标题
                    if not chinese_title:
                        title_part = remaining[end_bracket + 1:].strip()
                        if ' | ' in title_part:
                            chinese_title = title_part.split(' | ')[0].strip()
                        else:
                            chinese_title = title_part
                        logger.warning(f"未找到中文标题，使用英文标题: {chinese_title}")
                    # 不打印每个提取的标题，减少输出

                    # 清理标题，用于文件命名
                    clean_title = sanitize_filename(chinese_title)

                    videos_info.append((video_id, chinese_title, clean_title, channel_name))
                else:
                    logger.warning(f"跳过格式不正确的行: {line[:50]}...")

            except Exception as e:
                logger.error(f"解析第 {line_num} 行时出错: {line[:50]}... 错误: {e}")
                continue

    logger.warning(f"成功解析了 {len(videos_info)} 个视频记录")
    return videos_info

def check_subtitle_exists(channel_name, clean_title):
    """
    检查字幕文件是否已存在
    """
    # 检查SRT文件 - 格式：【频道名】标题.zh-Hans.srt
    srt_filename = f"【{channel_name}】{clean_title}.zh-Hans.srt"
    srt_path = f"D:/ytb_python_download/srt_han/{srt_filename}"

    # 检查VTT文件 - 格式：【频道名】标题.zh-Hans.vtt
    vtt_filename = f"【{channel_name}】{clean_title}.zh-Hans.vtt"
    vtt_path = f"D:/ytb_python_download/vtt_han/{vtt_filename}"

    return os.path.exists(srt_path) or os.path.exists(vtt_path)

def download_subtitle_only(video_id, chinese_title, clean_title, channel_name):
    """
    完全按照原版流程下载字幕
    """
    video_url = f'https://www.youtube.com/watch?v={video_id}'

    logger.warning(f"开始下载字幕: {chinese_title}")
    logger.warning(f"视频URL: {video_url}")

    # 无限重试直到成功
    attempt = 0
    while True:
        attempt += 1
        try:
            logger.warning(f"下载中文字幕，尝试第 {attempt} 次...")

            # 构建完整的输出路径，保存到正确的VTT目录
            vtt_filename_template = f'【{channel_name}】{clean_title}.%(ext)s'
            vtt_file_path = os.path.join('D:/ytb_python_download/vtt_han', vtt_filename_template)

            # 生成 .srt 文件路径，保存到正确的SRT目录
            srt_filename = f'【{channel_name}】{clean_title}.zh-Hans.srt'
            srt_file_path = os.path.join('D:/ytb_python_download/srt_han', srt_filename)

            # 如果SRT文件已存在，则跳过下载
            if os.path.exists(srt_file_path):
                logger.warning(f"✅ 中文字幕文件已存在，跳过下载: {srt_file_path}")
                return True

            logger.warning("开始下载简体中文 (zh-Hans) 字幕...")

            # 构建下载命令（完全按照原版）
            command = [
                'yt-dlp',
                '--cookies-from-browser', 'firefox',
            ]
            if USE_PROXY:
                command += ['--proxy', PROXY_URL]
            command += [
                '--write-auto-sub',
                '--skip-download',
                '--sub-lang', 'zh-Hans',         # 中文字幕
                '--sub-format', 'vtt',           # VTT
                '--socket-timeout', '60',
                '-o', vtt_file_path,
                video_url
            ]

            logger.warning(f"执行字幕下载命令: {' '.join(command)}")
            result = subprocess.run(command, capture_output=True, text=True)

            # 打印 yt-dlp 的标准输出和错误输出
            logger.warning(f"yt-dlp 标准输出:\n{result.stdout}")
            logger.warning(f"yt-dlp 错误输出:\n{result.stderr}")

            if result.returncode == 0:
                logger.warning("中文字幕下载成功。")

                # 确认 .vtt 文件存在且大小合理
                expected_vtt_path = vtt_file_path.replace('%(ext)s', 'zh-Hans.vtt')
                if os.path.exists(expected_vtt_path) and os.path.getsize(expected_vtt_path) > 100:
                    logger.warning(f"中文字幕文件已成功下载: {expected_vtt_path}")

                    # 导入原版的转换函数
                    import sys
                    sys.path.append('..')
                    from subtitle import convert_vtt_to_srt

                    # 转换 vtt 到 srt（使用原版函数）
                    convert_vtt_to_srt(expected_vtt_path, srt_file_path, max_length=26)

                    logger.warning(f"✅ 字幕下载和转换成功: {chinese_title}")
                    return True  # 成功时退出循环
                else:
                    logger.error(f"中文字幕文件未生成或文件过小: {expected_vtt_path}")
            else:
                logger.error(f"字幕下载失败，命令执行返回错误码: {result.returncode}")

        except subprocess.CalledProcessError as e:
            logger.error(f"下载简体中文字幕时命令执行失败（尝试 {attempt}）：{e}")
            logger.error(f"错误输出: {e.stderr}")
        except Exception as e:
            logger.error(f"下载简体中文字幕时发生未知错误（尝试 {attempt}）：{e}")

        # 等待后继续重试（无限重试）
        retry_delay = min(30 + (attempt - 1) * 10, 120)  # 递增延迟，最大120秒
        logger.warning(f"等待 {retry_delay} 秒后重试...")
        import time
        time.sleep(retry_delay)
        # 继续下一次循环尝试



def batch_download_subtitles_for_history(max_count=None, start_from=0):
    """
    为历史记录中的所有视频批量下载字幕
    
    Args:
        max_count: 最大处理数量，None表示处理所有
        start_from: 从第N个开始处理（用于断点续传）
    """
    logger.warning("=" * 60)
    logger.warning("开始为历史记录批量下载字幕")
    logger.warning("=" * 60)
    
    # 解析历史记录
    videos_info = parse_old_format_history()
    
    if not videos_info:
        logger.error("没有找到任何视频记录")
        return
    
    # 优先处理红仙子和Whispers的视频
    priority_channels = ["RedFairy红仙子", "Whispers"]
    priority_videos = [v for v in videos_info if v[3] in priority_channels]
    other_videos = [v for v in videos_info if v[3] not in priority_channels]

    # 重新排序：优先频道在前
    videos_info = priority_videos + other_videos
    logger.warning(f"优先处理频道视频: {len(priority_videos)} 个 (红仙子 + Whispers)")
    logger.warning(f"其他视频: {len(other_videos)} 个")

    # 应用起始位置和数量限制
    if start_from > 0:
        videos_info = videos_info[start_from:]
        logger.warning(f"从第 {start_from + 1} 个视频开始处理")

    if max_count:
        videos_info = videos_info[:max_count]
        logger.warning(f"限制处理数量: {max_count}")
    
    # 统计信息
    total_count = len(videos_info)
    success_count = 0
    skip_count = 0
    fail_count = 0
    
    logger.warning(f"准备处理 {total_count} 个视频的字幕下载")
    
    for i, (video_id, chinese_title, clean_title, channel_name) in enumerate(videos_info, 1):
        current_index = start_from + i
        logger.warning(f"\n进度: {i}/{total_count} (总进度: {current_index})")
        logger.warning(f"处理视频: {video_id}")
        logger.warning(f"频道: [{channel_name}]")
        logger.warning(f"中文标题: {chinese_title}")
        
        # 检查是否已有字幕
        if check_subtitle_exists(channel_name, clean_title):
            logger.warning(f"⏭️  跳过（已有字幕）: {chinese_title}")
            skip_count += 1
            continue

        # 下载字幕
        if download_subtitle_only(video_id, chinese_title, clean_title, channel_name):
            success_count += 1
        else:
            fail_count += 1
        
        # 每5个视频显示一次进度
        if i % 5 == 0:
            logger.warning(f"📊 当前进度: 成功 {success_count}, 跳过 {skip_count}, 失败 {fail_count}")
    
    # 输出最终统计结果
    logger.warning("\n" + "=" * 60)
    logger.warning("批量字幕下载完成")
    logger.warning("=" * 60)
    logger.warning(f"总计处理: {total_count} 个视频")
    logger.warning(f"成功下载: {success_count} 个字幕")
    logger.warning(f"跳过处理: {skip_count} 个（已有字幕）")
    logger.warning(f"下载失败: {fail_count} 个")
    logger.warning(f"成功率: {success_count/(total_count-skip_count)*100:.1f}%" if total_count > skip_count else "N/A")
    logger.warning("=" * 60)

def main():
    """
    主函数 - 批量下载所有历史视频的字幕，优先红仙子和Whispers，无限重试
    """
    print("=" * 60)
    print("无字幕版本 - 字幕补充下载器")
    print("开始为所有147个历史视频下载中文字幕")
    print("优先下载红仙子和Whispers频道，无限重试直到成功")
    print("=" * 60)

    # 下载所有视频的字幕，不限制数量，无限重试
    batch_download_subtitles_for_history(max_count=None, start_from=0)

if __name__ == "__main__":
    main()
